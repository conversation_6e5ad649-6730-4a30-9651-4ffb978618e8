import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/isolate/clean_http_client_adapter.dart';
import 'package:x1440/frameworks/notifications/models/in_app_message_notification.dart';
import 'package:x1440/frameworks/routing/routing_manager.dart';
import 'package:x1440/models/in_app_notification_model.dart';
import 'package:x1440/models/legacy/legacy_liveagentkit_types.dart';
import 'package:x1440/models/pigeon_messages.g.dart';
import 'package:x1440/models/sf_id.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/repositories/message_queue/message_queue_repository.dart';
import 'package:x1440/repositories/message_queue/models/queue_receive_message.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/ui/blocs/auth/auth_state.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/ui/notifications/in_app_notification_overlay.dart';
import 'package:x1440/ui/notifications/new_message_notification.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/notifications/notifications_use_case.dart';
import 'package:x1440/utils/extensions/iterable_extension.dart';

import 'models/displayed_notification.dart';
import 'notifications_event.dart';
import 'notifications_state.dart';

/// Refactor Note: we have a separate UseCase that is not used here; used elsewhere. Most of the logic here belongs in (that) UseCase
class NotificationManager extends Bloc<NotificationsEvent, NotificationsState>
    implements MessageFlutterApi {
  final RoutingManager _routingManager;
  final ContactsUseCase _contactsUseCase;
  final AppLifeCycleRepository _appLifeCycleRepository;
  final RemoteLogger _remoteLogger;
  final ConversationsUseCase _conversationsUseCase;

  NotificationManager(
    this._routingManager,
    this._contactsUseCase,
    this._conversationsUseCase,
    this._appLifeCycleRepository,
    this._remoteLogger,
  ) : super(NotificationsState()) {
    on<ClearNotificationsEvent>(_onClearNotificationsEvent);
    on<HandleAppOpenNotificationsEvent>(_onGetUnacknowledgedNotificationsEvent);
    on<HandleMessagesWithUnprocessedActionsEvent>(
        _onHandleMessagesWithUnprocessedActionsEvent);
    on<ReportMessageReceivedEvent>(_onReportMessageReceivedEvent);
    on<ReportMessageNotificationNeedsDisplayEvent>(
        _onReportMessageNotificationNeedsDisplayEvent);
    on<RemoveNotificationByConversationIdEvent>(
        _onRemoveNotificationByConversationIdEvent);
    on<RemoveNotificationByWorkTargetIdEvent>(
        _onRemoveNotificationByWorkTargetId);
    on<AcknowledgeNotificationsEvent>(_onAcknowledgeNotificationsEvent);
    on<AcknowledgeNotificationsForConversationEvent>(
        _onAcknowledgeNotificationsForConversationEvent);

    // Set up auth state listener to process pending notification actions after login
    _setupAuthStateListener();
  }

  // TODO: when included in BaseModules, this creates a circular dependency; needs refactoring
  ConversationsBloc get _conversationsBloc => GetIt.I<ConversationsBloc>();

  MessagingUseCase get _messagingUseCase => GetIt.I<MessagingUseCase>();

  /// Set up auth state listener to automatically process pending notification actions after login
  void _setupAuthStateListener() {
    try {
      // Check if AuthBloc is available
      if (GetIt.I.isRegistered<AuthBloc>()) {
        _remoteLogger.info('🔄 NOTIFICATION_AUTH_LISTENER: Setting up auth state listener for pending notification actions');

        // Listen to AuthBloc state changes
        GetIt.I<AuthBloc>().stream.listen((authState) {
          _remoteLogger.info('🔄 NOTIFICATION_AUTH_LISTENER: Auth state changed to: ${authState.status}');

          // Check if user just logged in and we have pending notification actions
          if (authState.status == AuthStatus.loggedIn &&
              state.messagesWithUnprocessedActions.isNotEmpty) {
            _remoteLogger.info('🔄 NOTIFICATION_AUTH_LISTENER: User logged in with ${state.messagesWithUnprocessedActions.length} pending notification actions, processing now');

              if (_routingManager.isLoggedIn && state.messagesWithUnprocessedActions.isNotEmpty) {
                _remoteLogger.info('✅ NOTIFICATION_AUTH_LISTENER: Processing ${state.messagesWithUnprocessedActions.length} pending notification actions');
                add(HandleMessagesWithUnprocessedActionsEvent());
              }
          }
        });
      } else {
        _remoteLogger.warn('⚠️ NOTIFICATION_AUTH_LISTENER: AuthBloc not registered, cannot set up auth state listener');
      }
    } catch (e) {
      _remoteLogger.error('❌ NOTIFICATION_AUTH_LISTENER: Error setting up auth state listener: $e');
    }
  }

  Future<void> init() async {
    /// listen for system push notifications
    MessageFlutterApi.setup(this);

    /// if we need to hide/show on app background/foreground, this is a good start
    // _appLifeCycleRepository.appLifeCycleStream
    //     .listen((AppLifecycleState? state) {
    //   if (state == AppLifecycleState.resumed) {
    //     /// show any needed notifications on foreground
    //     _showNextInAppNotification();
    //   } else {
    //     /// remove any currently displayed notifications & save for app foreground
    //     List<InAppNotification? Function()> removeOverlayFuncs =
    //         _removeOverlayFuncs.toList();
    //     _removeOverlayFuncs.clear();
    //     for (var element in removeOverlayFuncs) {
    //       var notification = element();
    //       if (notification != null
    //           // && !_displayedNotifications.contains(notification)
    //       ) {
    //         _notificationsToDisplay.add(notification);
    //       }
    //     }
    //     _displayedNotifications.clear();
    //   }
    // });
  }

  void _onClearNotificationsEvent(
      ClearNotificationsEvent event, Emitter<NotificationsState> emit) {
    final List<DisplayedNotification> displayedNotifications =
        List.from(_displayedNotifications);
    for (var displayedNotification in displayedNotifications) {
      displayedNotification.removeOverlayCallback();
    }
    _displayedNotifications.clear();
    _notificationsToDisplay.clear();
    _receivedNotifications.clear();

    emit(state.copyWith(unacknowledgedNotifications: []));
  }

  void _onGetUnacknowledgedNotificationsEvent(
      HandleAppOpenNotificationsEvent event,
      Emitter<NotificationsState> emit) async {
    final unacknowledgedNotifications = await GetIt.I<NotificationsUseCase>()
        .handleAppOpen(event.appLifecycleState);
  }

  void _onHandleMessagesWithUnprocessedActionsEvent(
      HandleMessagesWithUnprocessedActionsEvent event,
      Emitter<NotificationsState> emit) {
    for (var queueReceiveMessage in state.messagesWithUnprocessedActions) {
      _remoteLogger.info(
          'received notification with ${queueReceiveMessage.notificationAction}; not showing in-app notification; sending to legacy handling');
      GetIt.I<MessageQueueRepository>()
          .addMessageToReceiveQueue(queueReceiveMessage);
    }
    emit(state.copyWith(messagesWithUnprocessedActions: []));
  }

  void _onReportMessageReceivedEvent(ReportMessageReceivedEvent event,
      Emitter<NotificationsState> emit) async {
    InAppMessageNotification? parsedNotification =
        InAppMessageNotification.fromQueueAndShim(
            event.queueReceivedMessage, event.shimServicePayload);

    final SfId? conversationId =
        (await _conversationsUseCase.getConversationByMessagingSessionId(
                parsedNotification?.messagingSessionId))
            ?.id;

    if (parsedNotification?.conversationId == null && conversationId != null) {
      parsedNotification =
          parsedNotification?.copyWith(conversationId: conversationId);
    }

    List<InAppMessageNotification> receivedMessages =
        List.from(state.unacknowledgedNotifications);
    if (parsedNotification != null) {
      final existingMessage = receivedMessages.firstWhereOrNull(
          (receivedMessage) =>
              receivedMessage.pushNotificationId != null &&
              parsedNotification?.pushNotificationId != null &&
              int.parse(receivedMessage.pushNotificationId!) ==
                  int.parse(parsedNotification!.pushNotificationId!));

      if (existingMessage != null) {
        receivedMessages.remove(existingMessage);
        parsedNotification = parsedNotification.copyWith(
          notificationAction: existingMessage.notificationAction ??
              parsedNotification.notificationAction,
        );
      }
      receivedMessages.add(parsedNotification);

      // FALLBACK NAVIGATION: Check if this message corresponds to a pending notification tap
      if (_pendingNotificationTapId != null &&
          parsedNotification.pushNotificationId == _pendingNotificationTapId) {
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - FALLBACK: Found message for pending notification tap: $_pendingNotificationTapId');

        // Get the conversation ID for navigation
        final navigationConversationId = parsedNotification.conversationId ?? conversationId;

        if (navigationConversationId != null) {
          _remoteLogger.info('🔔 NOTIFICATION_ROUTING - FALLBACK: Triggering navigation to conversation: $navigationConversationId');

          // Use the ConversationsBloc pending navigation system
          if (GetIt.I.isRegistered<ConversationsBloc>()) {
            final conversationsBloc = GetIt.I<ConversationsBloc>();
            conversationsBloc.add(GoToConversationEvent(navigationConversationId));
            _remoteLogger.info('🔔 NOTIFICATION_ROUTING - FALLBACK: GoToConversationEvent added successfully');
          } else {
            _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - FALLBACK: ConversationsBloc not registered');
          }
        } else {
          _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - FALLBACK: No conversation ID available for navigation');
        }

        // Clear the pending notification tap
        _pendingNotificationTapId = null;
      }
    }
    emit(state.copyWith(unacknowledgedNotifications: receivedMessages));
    _showNextInAppNotification();
  }

  void _onAcknowledgeNotificationsEvent(
      AcknowledgeNotificationsEvent event, Emitter<NotificationsState> emit) {
    _messagingUseCase.acknowledgeNotifications(event.notificationIds);

    List<InAppMessageNotification> receivedMessages =
        List.from(state.unacknowledgedNotifications);
    receivedMessages.removeWhere((receivedMessage) {
      return event.notificationIds.contains(receivedMessage.pushNotificationId);
    });
    emit(state.copyWith(unacknowledgedNotifications: receivedMessages));
  }

  void _onAcknowledgeNotificationsForConversationEvent(
      AcknowledgeNotificationsForConversationEvent event,
      Emitter<NotificationsState> emit) {
    /// clear any displayed notifications for this conversation
    final List<DisplayedNotification> displayedNotifications =
        List.from(_displayedNotifications);
    for (var displayedNotification in displayedNotifications) {
      var notification = displayedNotification.notification;
      if (notification.conversationId == event.conversationId) {
        displayedNotification.removeOverlayCallback();
        _displayedNotifications
            .removeWhere((element) => element.notification == notification);
      }
    }
    var matchedMessages = state.unacknowledgedNotifications
        .where((element) => element.conversationId == event.conversationId);
    if (matchedMessages.isNotEmpty) {
      _messagingUseCase.acknowledgeNotifications(
          matchedMessages.map((e) => e.pushNotificationId!).toList());
      List<InAppMessageNotification> receivedMessages =
          List.from(state.unacknowledgedNotifications);
      receivedMessages.removeWhere(
          (element) => element.conversationId == event.conversationId);
      emit(state.copyWith(unacknowledgedNotifications: receivedMessages));
    }
  }

  final List<QueueReceiveMessage> _receivedNotifications = [];

  /// notifications that are "currently" displayed: have been shown but not yet dismissed (only removed on dismiss -- incl accept/decline/tap -- event)
  final List<DisplayedNotification> _displayedNotifications = [];
  final List<InAppNotification> _notificationsToDisplay = [];

  /// Track pending notification taps for navigation fallback
  String? _pendingNotificationTapId;

  void _showInAppNotification(
    InAppNotification notification,
  ) async {
    _remoteLogger.info(
        'showing notification; logged in? ${_routingManager.isLoggedIn}: $notification');

    /// assemble notification overlay
    OverlayEntry? notificationOverlay;
    InAppNotification removeNotificationCallback() {
      try {
        _displayedNotifications
            .removeWhere((element) => element.notification == notification);
        _notificationsToDisplay.remove(notification);

        if (notificationOverlay?.mounted == true) {
          notificationOverlay?.remove();
        }
      } catch (e) {
        _remoteLogger.warn(
            '(Flutter engine bug?) error removing notification overlay: $e');
      }
      _showNextInAppNotification();
      return notification;
    }

    NewMessageNotificationOverlay notificationWidget =
        notification.builder(removeNotificationCallback);

    var cvmConversation = notificationWidget.workTargetId == null &&
            notificationWidget.conversationId == null
        ? null
        : (await _getConversationFromConversationsViewmodel(
            notificationWidget.workTargetId ??
                notificationWidget.conversationId));

    /// workaround for old shim architecture not passing objects neatly // TODO: remove/update on shim refactor
    final matchedNotification =
        _getNotificationForNewMessageNotificationOverlay(notificationWidget);

    notification = notification.copyWith(
        notificationId: matchedNotification?.pushNotificationId,
        messageType: matchedNotification?.messageType,
        workId: matchedNotification?.workId);

    String? notificationId =
        notification.notificationId ?? notificationWidget.notificationId;

    _remoteLogger.info(
        'found matchedNotification id: $notificationId; messageType: ${notification.messageType}; workId: ${notification.workId}; conversationId: ${notification.conversationId}; notificationWidget.conversationId: ${notificationWidget.conversationId}; notificationWidget.workTargetId: ${notificationWidget.workTargetId}; notificationWidget.messageType: ${notificationWidget.messageType}; notificationWidget.notificationId: ${notificationWidget.notificationId}; notificationWidget.workId: ${notificationWidget.workId}; notificationWidget.channelPictureAssetPath: ${notificationWidget.channelPictureAssetPath}; conversationScrtUuid: ${notificationWidget.conversationScrtUuid}; conversationId: ${notificationWidget.conversationId}; conversationScrtUuid: ${notificationWidget.conversationScrtUuid}');
    notification = notification.copyWith(notificationId: notificationId);

    /// go to correct page if from tapped notification
    if (notificationId != null &&
        _navigateToPageIfTappedNotification(notification,
            cvmConversation?.sfId ?? notificationWidget.conversationId!)) {
      return;
    }

    /// don't show this notification if we're already on the right chat screen ...
    if (!_getIfNotificationNeedsDisplay(notificationWidget, cvmConversation)) {
      /// still need to Acknowledge it. // TODO: CLEAN this up?
      if (notificationId != null) {
        add(AcknowledgeNotificationsEvent([notificationId]));
      }
      return _showNextInAppNotification();
    }

    if (notificationWidget.body.isEmpty) {
      var matchedMessage = _getMessageForInAppNotification(notification);
      if (matchedMessage?.body.isNotEmpty != true) {
        throw Exception(
            'trying to show Notification with empty body: ${notificationWidget.notificationId}: $notificationWidget');
      }
      notificationWidget = notificationWidget.copyWith(
        body: matchedMessage!.body,
      );
    }

    if (notificationWidget.title.isEmpty) {
      var matchedMessage = _getMessageForInAppNotification(notification);
      if (matchedMessage?.title.isNotEmpty != true) {
        throw Exception(
            'trying to show Notification with empty title: ${notificationWidget.notificationId}: $notificationWidget');
      }
      notificationWidget = notificationWidget.copyWith(
        title: matchedMessage!.title,
      );
    }

    /// build notification & display
    notificationOverlay = OverlayEntry(
        builder: (context) => InAppNotificationOverlay(
              onDismissed: (DismissDirection dismissDirection) {
                removeNotificationCallback();
                notification.onDismissed?.call(dismissDirection);
              },
              child: notificationWidget,
            ));

    _displayedNotifications.add(DisplayedNotification(
        notification: notification,
        removeOverlayCallback: removeNotificationCallback));
    if (!_routingManager.isLoggedIn ||
        !_routingManager.insertOverlay(notificationOverlay)) {
      add(ClearNotificationsEvent());
    }
  }

  void _showNextInAppNotification() {
    if (_notificationsToDisplay.isEmpty) {
      return;
    }

    InAppNotification notification = _notificationsToDisplay.removeAt(0);
    _showInAppNotification(notification);
  }

  /// Fetch the full notification payload and process it
  void _fetchAndProcessNotification(String notificationId, String? categoryId, String? action) async {
    try {
      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - _fetchAndProcessNotification: Fetching notification message for ID: $notificationId');

      // Track this notification tap for fallback navigation
      _pendingNotificationTapId = notificationId;
      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Tracking pending notification tap: $notificationId');

      // Set a timeout to clear the pending notification tap if no matching message is received
      Timer(const Duration(seconds: 30), () {
        if (_pendingNotificationTapId == notificationId) {
          _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - TIMEOUT: Clearing pending notification tap after 30 seconds: $notificationId');
          _pendingNotificationTapId = null;
        }
      });

      // CRITICAL FIX: Wait for authentication to be ready before making API calls
      // This prevents "Invalid token" errors when app is opened from notification tap
      await _waitForAuthenticationReady();

      // Use the NotificationsUseCase to fetch the notification message
      final notificationResponse = await GetIt.I<NotificationsUseCase>().getNotificationMessage(notificationId);

      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Successfully fetched notification message: ${notificationResponse.message}');

      // Parse the notification message payload
      final messagePayload = notificationResponse.message;
      if (messagePayload != null && messagePayload.isNotEmpty) {
        // Parse the JSON payload to extract sessionId for navigation
        Map<String, dynamic>? parsedPayload;
        try {
          parsedPayload = jsonDecode(messagePayload) as Map<String, dynamic>?;
          _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Parsed payload JSON: $parsedPayload');
        } catch (e) {
          _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - Failed to parse notification payload JSON: $e');
        }

        // Convert the message payload to a map for processing
        final Map<String?, Object?> fullPayload = {
          'payload': messagePayload,
          'notificationId': notificationId,
          'categoryId': categoryId,
          'isNotificationTap': true,
          // Include sessionId for navigation if available
          if (parsedPayload?['sessionId'] != null) 'sessionId': parsedPayload!['sessionId'],
        };

        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Processing fetched notification with full payload: $fullPayload');

        // Process the notification with the full payload
        QueueReceiveMessage? queueReceiveMessage;
        try {
          queueReceiveMessage = QueueReceiveMessage.fromPushNotification(
              categoryId ?? 'noCategoryId',
              action?.isNotEmpty == true
                  ? action!
                  : "io.x1440.ACTION_NOTIFICATION_DEFAULT",
              fullPayload);
          _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Successfully created QueueReceiveMessage: ${queueReceiveMessage?.shimServicePayload}');
        } catch (e) {
          _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - failed to parse fetched notification into queueReceiveMessage: $e');
          return;
        }

        // Continue with the normal notification processing flow
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Calling _processQueueReceiveMessage');
        _processQueueReceiveMessage(queueReceiveMessage, action);

        // Clear pending notification tap since primary navigation should work
        _pendingNotificationTapId = null;
      } else {
        _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - Fetched notification message is empty for ID: $notificationId');
      }
    } catch (e) {
      _remoteLogger.error('🔔 NOTIFICATION_ROUTING - Failed to fetch notification message for ID: $notificationId, error: $e');
    }
  }

  /// Process a QueueReceiveMessage with the standard notification flow
  void _processQueueReceiveMessage(QueueReceiveMessage? queueReceiveMessage, String? action) {
    _remoteLogger.info('🔔 NOTIFICATION_ROUTING - _processQueueReceiveMessage called with action: $action');
    _remoteLogger.info('🔔 NOTIFICATION_ROUTING - QueueReceiveMessage payload: ${queueReceiveMessage?.shimServicePayload}');

    if (queueReceiveMessage?.shimServicePayload['messageType'] == '1440/expired') {
      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Processing expired message');
      if (_routingManager.isLoggedIn) {
        GetIt.I<MessageQueueRepository>().addMessageToReceiveQueue(queueReceiveMessage!);
      } else {
        return;
      }
    } else if (_appLifeCycleRepository.isForegrounded &&
        _routingManager.isLoggedIn &&
        action != "DISCONNECT_ACTION") {
      _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - received push notification while in foreground, attempting to reconnect websocket');
      GetIt.I<MessagingUseCase>().connectToWebsocket();
      GetIt.I<NotificationsUseCase>().getUnacknowledgedNotifications();
      return;
    }

    // Process the notification normally
    if (queueReceiveMessage != null) {
      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Processing notification normally');
      _receivedNotifications.add(queueReceiveMessage);
      add(ReportMessageReceivedEvent(queueReceivedMessage: queueReceiveMessage));

      // Check if this is a notification tap that should navigate to a conversation
      final isNotificationTap = queueReceiveMessage.shimServicePayload['isNotificationTap'] == true;
      final conversationId = queueReceiveMessage.shimServicePayload['sessionId'] as String?;

      _remoteLogger.info('🔔 NOTIFICATION_ROUTING - isNotificationTap: $isNotificationTap, conversationId: $conversationId');

      if (isNotificationTap && conversationId != null && conversationId.isNotEmpty) {
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Notification tap detected, scheduling navigation to conversation: $conversationId');
        // Use the ConversationsBloc pending navigation system to ensure conversations are loaded first
        if (GetIt.I.isRegistered<ConversationsBloc>()) {
          final conversationsBloc = GetIt.I<ConversationsBloc>();
          _remoteLogger.info('🔔 NOTIFICATION_ROUTING - ConversationsBloc found, adding GoToConversationEvent');
          conversationsBloc.add(GoToConversationEvent(SfId(conversationId)));
        } else {
          _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - ConversationsBloc not registered, cannot navigate to conversation');
        }
      } else {
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - No navigation needed: isNotificationTap=$isNotificationTap, conversationId=$conversationId');
      }

      if (queueReceiveMessage.notificationAction == 'ACCEPT_ACTION' ||
          queueReceiveMessage.notificationAction == 'DECLINE_ACTION') {
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - received notification with ${queueReceiveMessage.notificationAction}; not showing in-app notification; sending to legacy handling');
        if (_routingManager.isLoggedIn) {
          GetIt.I<MessageQueueRepository>().addMessageToReceiveQueue(queueReceiveMessage);
        } else {
          emit(state.copyWith(messagesWithUnprocessedActions: [
            ...state.messagesWithUnprocessedActions,
            queueReceiveMessage
          ]));
        }
      }
    } else {
      _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - queueReceiveMessage is null, cannot process');
    }
  }

  void _onReportMessageNotificationNeedsDisplayEvent(
      ReportMessageNotificationNeedsDisplayEvent event,
      Emitter<NotificationsState> emit) async {
    final inAppMessageNotification = event.inAppMessageNotification;
    final bool isWorkOffered =
        inAppMessageNotification.messageType == MessageType.work;

    var cvmConversation = await _getConversationFromConversationsViewmodel(
        inAppMessageNotification.workTargetId);
    SfId? conversationId =
        inAppMessageNotification.conversationId ?? cvmConversation?.id.toSfId();

    if (conversationId == null) {
      _remoteLogger.warn(
          'failed to get conversationId for notification: $inAppMessageNotification');
      return;
    }

    var matchedMessage =
        _getMessageForInAppMessageNotification(inAppMessageNotification);

    SfId? messagingEndUserId = cvmConversation?.id.toSfId();
    String? conversationScrtUuid = cvmConversation?.scrtUUID;

    SavedContactByMeu? savedContact;
    if (messagingEndUserId != null) {
      savedContact = _contactsUseCase
          .getSavedContactByMessagingEndUserId(messagingEndUserId);
    }

    String messageBody = matchedMessage?.body.isNotEmpty == true
        ? matchedMessage!.body
        : inAppMessageNotification.body;
    String messageTitle = matchedMessage?.title.isNotEmpty == true
        ? matchedMessage!.title
        : inAppMessageNotification.title;

    void acknowledgeNotification() {
      if (inAppMessageNotification.pushNotificationId == null) {
        _remoteLogger.warn(
            'trying to acknowledge notification without pushNotificationId: $inAppMessageNotification');
      } else {
        add(AcknowledgeNotificationsEvent(
            [inAppMessageNotification.pushNotificationId!]));
      }
    }

    NewMessageNotificationOverlay notificationBuilder(
        void Function() removeNotification) {
      NewMessageNotificationOverlay notification =
          NewMessageNotificationOverlay.fromInAppMessageNotification(
        notification: inAppMessageNotification.copyWith(
          conversationScrtUuid: conversationScrtUuid ??
              inAppMessageNotification.conversationScrtUuid,
          conversationId: matchedMessage?.conversationId ??
              inAppMessageNotification.conversationId,
          body: messageBody,
          title: messageTitle,
          channelType: matchedMessage?.channelType ??
              cvmConversation?.channelType ??
              inAppMessageNotification.channelType,
          userPhotoUrl: matchedMessage?.userPhotoUrl ??
              savedContact?.value.photoUrl ??
              inAppMessageNotification.userPhotoUrl,
          pushNotificationId: matchedMessage?.pushNotificationId ??
              inAppMessageNotification.pushNotificationId,
        ),
        onTap: isWorkOffered
            ? null
            : () {
                _remoteLogger.info(
                    'tapped message from notification; conversationId: $conversationId');
                _routingManager.goToChatScreen(conversationId, null);
                removeNotification();
                acknowledgeNotification();
              },
        onTapCancel: !isWorkOffered
            ? null
            : () {
                _conversationsBloc
                    .add(DeclineConversationEvent(conversationId));
                removeNotification();
                acknowledgeNotification();
              },
        onTapConfirm: !isWorkOffered
            ? null
            : () {
                _remoteLogger.info(
                    'tapped accept work from notification; conversationId: $conversationId');
                _conversationsBloc
                    .add(AcceptConversationEvent(conversationId, true));
                removeNotification();
                acknowledgeNotification();
              },
      );
      return notification;
    }

    _notificationsToDisplay.add(InAppNotification(
        conversationId: conversationId,
        notificationId: matchedMessage?.pushNotificationId ??
            inAppMessageNotification.pushNotificationId,
        messageType: isWorkOffered ? MessageType.work : MessageType.message,
        builder: notificationBuilder,
        onDismissed: (DismissDirection dismissDirection) {
          acknowledgeNotification();
          if (isWorkOffered) {
            _remoteLogger.info('work declined from notification dismiss');
            _conversationsBloc.add(DeclineConversationEvent(conversationId));
          }
        }));
    _showNextInAppNotification();
  }

  void _onRemoveNotificationByConversationIdEvent(
      RemoveNotificationByConversationIdEvent event,
      Emitter<NotificationsState> emit) {
    final conversationId = event.conversationId;
    final notificationId = state.unacknowledgedNotifications
            .firstWhereOrNull(
                (element) => element.conversationId == conversationId)
            ?.pushNotificationId ??
        _displayedNotifications
            .firstWhereOrNull((element) =>
                element.notification.conversationId == conversationId)
            ?.notification
            .notificationId;
    if (notificationId != null) {
      removeNotification(notificationId);
    }
  }

  void _onRemoveNotificationByWorkTargetId(
      RemoveNotificationByWorkTargetIdEvent event,
      Emitter<NotificationsState> emit) {
    final workTargetId = event.workTargetId;
    final notificationId = state.unacknowledgedNotifications
        .firstWhereOrNull((element) => element.workTargetId == workTargetId)
        ?.pushNotificationId;
    if (notificationId != null) {
      removeNotification(notificationId);
    }
  }

  InAppMessageNotification? _getMatchedMessage({
    required String notificationId,
  }) {
    for (var receivedMessage in state.unacknowledgedNotifications) {
      if (receivedMessage.pushNotificationId == notificationId) {
        return receivedMessage;
      }
    }
    return null;
  }

  InAppMessageNotification? _getMessageForInAppMessageNotification(
      InAppMessageNotification notification) {
    if (notification.pushNotificationId == null) {
      if (notification.messageType == MessageType.work) {
        return state.unacknowledgedNotifications.firstWhereOrNull((element) =>
            element.messageType == MessageType.work &&
            element.workId == notification.workId);
      }
      return null;
    }
    return _getMatchedMessage(notificationId: notification.pushNotificationId!);
  }

  InAppMessageNotification? _getMessageForInAppNotification(
      InAppNotification notification) {
    if (notification.notificationId == null) {
      if (notification.messageType == MessageType.work) {
        return state.unacknowledgedNotifications.firstWhereOrNull((element) =>
            element.messageType == MessageType.work &&
            element.workId != null &&
            element.workId == notification.workId);
      }
      return null;
    }
    return _getMatchedMessage(notificationId: notification.notificationId!);
  }

  /// catch UI/system push notifications actions from pigeon
  @override
  void pushActionSelection(
      String? categoryId, String? action, Map<String?, Object?>? payload) {
    _remoteLogger.info(
        '🔔 NOTIFICATION_ROUTING - pushActionSelection called: notificationId: ${payload?['notificationId']} Category Id: $categoryId Action: $action; isNull? ${action == null} Payload: $payload');

    // Check if we need to fetch the full notification payload
    final needsFetch = payload?['needsFetch'] as bool? ?? false;
    final isNotificationTap = payload?['isNotificationTap'] as bool? ?? false;

    _remoteLogger.info('🔔 NOTIFICATION_ROUTING - needsFetch: $needsFetch, isNotificationTap: $isNotificationTap');

    if (needsFetch && isNotificationTap) {
      final notificationId = payload?['notificationId'] as String?;
      if (notificationId != null) {
        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Fetching full notification payload for ID: $notificationId');
        _fetchAndProcessNotification(notificationId, categoryId, action);
        return;
      } else {
        _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - needsFetch is true but notificationId is null');
      }
    }

    QueueReceiveMessage? queueReceiveMessage;
    try {
      queueReceiveMessage = QueueReceiveMessage.fromPushNotification(
          categoryId ?? 'noCategoryId',

          /// Note: on iOS Tapped notifications have been received with a null action, so we were hard-setting them to "DEFAULT_ACTION", but that caused issues w/ auto-navigating to un-tapped notifications (observed in Android) /// UPDATE early 2025: this is no longer the case?
          action?.isNotEmpty == true
              ? action!
              : "io.x1440.ACTION_NOTIFICATION_DEFAULT",
          // action ?? (Platform.isIOS ? "DEFAULT_ACTION" : null),
          payload);
    } catch (e) {
      _remoteLogger
          .warn('failed to parse notification into queueReceiveMessage: $e');
      return;
    }

    // Handle PING_ACTION separately
    if (categoryId == "PING_ACTION") {
      if (action == "DISCONNECT_ACTION") {
        _remoteLogger
            .info('calling logout from PING_ACTION & DISCONNECT_ACTION');
        GetIt.I<AuthBloc>().add(LogoutEvent());
      } else {
        // Handle ping notification by automatically triggering keep-alive
        _remoteLogger.info('🔔 PING_ACTION received - automatically triggering keep-alive response');

        try {
          // Check if AuthBloc is available and user is logged in
          if (GetIt.I.isRegistered<AuthBloc>()) {
            final authBloc = GetIt.I<AuthBloc>();
            final currentState = authBloc.state;

            if (currentState.status == AuthStatus.loggedIn) {
              _remoteLogger.info('✅ PING_ACTION - User is logged in, triggering KeepSessionAliveEvent');
              authBloc.add(KeepSessionAliveEvent());
            } else {
              _remoteLogger.info('⚠️ PING_ACTION - User not logged in (${currentState.status}), skipping keep-alive');
            }
          } else {
            _remoteLogger.warn('⚠️ PING_ACTION - AuthBloc not registered, cannot trigger keep-alive');
          }
        } catch (e) {
          _remoteLogger.error('❌ PING_ACTION - Error triggering keep-alive: $e');
        }
      }
    } else {
      // Use the helper method for standard notification processing
      _processQueueReceiveMessage(queueReceiveMessage, action);
    }
  }

  bool _getIfNotificationNeedsDisplay(
      NewMessageNotificationOverlay notificationWidget,
      LakConversation? cvmConversation) {
    /// if its a work message, we want to show it (even if user is open to the conversation)
    if (notificationWidget.messageType == MessageType.work) {
      /// unless it's already been auto-accepted (from a user tapping "Accept" on the notification)
      if (_conversationsUseCase
          .conversationIsPendingAccepted(notificationWidget.workTargetId)) {
        _remoteLogger.info(
            'work message already auto-accepted; not showing notification');
        return false;
      }

      return true;
    }
    String? notificationId = notificationWidget.notificationId;
    SfId? currentUiConversationId = _routingManager.currentUiConversationId;
    String? conversationScrtUuid = cvmConversation?.scrtUUID;
    SfId? conversationMeuId = cvmConversation?.sfId;
    bool isCurrentScreenSameConversationAsMessage = () {
      if (currentUiConversationId == null) {
        _remoteLogger
            .info('currentUiConversationId is null; not showing notification');
        return false;
      }
      if (currentUiConversationId.toString().length > 18) {
        if (currentUiConversationId ==
                notificationWidget.conversationScrtUuid ||
            currentUiConversationId == conversationScrtUuid) {
          return true;
        }
        _remoteLogger.info(
            'currentUiConversationId is not a conversationId; not showing notification');
        return false;
      }
      if (currentUiConversationId == notificationWidget.conversationId ||
          currentUiConversationId == conversationMeuId) {
        return true;
      }
      _remoteLogger.info(
          'currentUiConversationId is not a conversationId2; not showing notification');
      return false;
    }();

    _remoteLogger.info(
        'Posting Notification with: currentUiConversationId: $currentUiConversationId; notificationId: $notificationId; notificationWidget.conversationId: ${notificationWidget.conversationId}; notificationWidget.conversationScrtUuid: ${notificationWidget.conversationScrtUuid} isCurrentScreenSameConversationAsMessage: $isCurrentScreenSameConversationAsMessage; channelPictureAssetPath: ${notificationWidget.channelPictureAssetPath} messageType: ${notificationWidget.messageType}; conversationScrtUuid: $conversationScrtUuid');

    /// ... or if we're already showing the notification
    if (isCurrentScreenSameConversationAsMessage ||
        _displayedNotifications
            .where((displayedNotification) =>
                displayedNotification.notification.notificationId != null &&
                displayedNotification.notification.notificationId ==
                    notificationId)
            .isNotEmpty) {
      return false;
    }
    return true;
  }

  /// check if notification has been tapped; if so, don't show this one, show next, & navigate to conversation
  bool _navigateToPageIfTappedNotification(
      InAppNotification notification, SfId messagingEndUserId) {
    var matchedMessage = _getMessageForInAppNotification(notification);
    if (matchedMessage?.messageType == MessageType.message

        /// Note: on Android, these tapped notifications are received with the "DEFAULT_ACTION" action, but on iOS, they have a null action, so we hard-set them to "DEFAULT_ACTION"
        &&
        matchedMessage?.notificationAction == 'DEFAULT_ACTION') {
      GetIt.I<ConversationsBloc>()
          .add(GoToConversationEvent(messagingEndUserId));
      _showNextInAppNotification();
      _remoteLogger.info(
          'tapped message from notification; conversationId: $messagingEndUserId');
      return true;
    }
    return false;
  }

  /// *** LEGACY SHIM SERVICE SUPPORT *** // TODO: remove section on shim refactor
  /// (to be removed when shim service is refactored)
  @Deprecated('to support legacy shim service')
  InAppMessageNotification? _getNotificationForNewMessageNotificationOverlay(
      NewMessageNotificationOverlay overlay) {
    if (overlay.messageType == MessageType.work) {
      return state.unacknowledgedNotifications.firstWhereOrNull((element) =>
          element.workId != null && element.workId == overlay.workId);
    }
    return state.unacknowledgedNotifications
        .firstWhereOrNull((element) => element.messageId == overlay.messageId);
  }

  @Deprecated('supporting legacy vm')
  Future<LakConversation?> _getConversationFromConversationsViewmodel(
      SfId? conversationId) async {
    try {
      return (await (_conversationsUseCase)
              .getConversationByMessagingSessionId(conversationId))
          ?.lakConversation;
    } catch (e) {
      _remoteLogger
          .warn('failed to get conversation from conversations viewmodel: $e');
      return null;
    }
  }

  /// Wait for authentication to be ready before making API calls
  /// This prevents "Invalid token" errors when app is opened from notification tap
  Future<void> _waitForAuthenticationReady() async {
    _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Waiting for authentication to be ready...');

    const maxWaitTime = Duration(seconds: 10);
    const checkInterval = Duration(milliseconds: 500);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      try {
        // Check if we have valid credentials
        final credentials = await GetIt.I<LocalStorageRepository>().getCredentials();

        // Check if AuthBloc is registered and user is logged in
        bool isAuthReady = false;
        if (GetIt.I.isRegistered<AuthBloc>()) {
          final authState = GetIt.I<AuthBloc>().state;
          isAuthReady = authState.status == AuthStatus.loggedIn;
        }

        // Check if we have the necessary tokens
        final hasAuthToken = credentials.authorizationToken != null && credentials.authorizationToken!.isNotEmpty;
        final hasSessionToken = credentials.sessionToken != null && credentials.sessionToken!.isNotEmpty;

        if (isAuthReady && (hasAuthToken || hasSessionToken)) {
          _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Authentication is ready! hasAuthToken: $hasAuthToken, hasSessionToken: $hasSessionToken');

          // Ensure network isolate has the tokens
          if (GetIt.I.isRegistered<CleanNetworkServiceFactory>()) {
            try {
              await GetIt.I<CleanNetworkServiceFactory>().updateAuthTokens(
                authorizationToken: credentials.authorizationToken,
                accessToken: credentials.sessionToken,
              );
              _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Updated network isolate with auth tokens');
            } catch (e) {
              _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - Failed to update network isolate tokens: $e');
            }
          }

          return;
        }

        _remoteLogger.info('🔔 NOTIFICATION_ROUTING - Auth not ready yet, waiting... isAuthReady: $isAuthReady, hasAuthToken: $hasAuthToken, hasSessionToken: $hasSessionToken');

      } catch (e) {
        _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - Error checking auth readiness: $e');
      }

      // Wait before checking again
      await Future.delayed(checkInterval);
    }

    _remoteLogger.warn('🔔 NOTIFICATION_ROUTING - Timeout waiting for authentication, proceeding anyway');
  }

  void removeNotification(String notificationId) {
    var displayedNotification = _displayedNotifications.firstWhereOrNull(
        (element) => element.notification.notificationId == notificationId);

    displayedNotification?.removeOverlayCallback();
    _displayedNotifications.removeWhere(
        (element) => element.notification.notificationId == notificationId);
    _notificationsToDisplay
        .removeWhere((element) => element.notificationId == notificationId);
  }
}
